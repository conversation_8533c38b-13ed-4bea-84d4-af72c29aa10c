from enum import StrEnum
from functools import cached_property
from typing import Any, ClassVar

from starlette.authentication import BaseUser
from tortoise import fields

from apps.exhi.models import ExhiHall
from config import SECRET_KEY
from libs.attrdict import AttrDict
from libs.orm import Model, relate_cache
from libs.utils import make_signature, verify_signature


class Role(StrEnum):
    """管理员角色枚举"""

    SUPER = '超级管理员'
    ADMIN = '普通管理员'
    MANAGER = '店长'
    STAFF = '员工'
    OPERATOR = '运营'


class Admin(Model, BaseUser):
    """管理员用户表"""

    username = fields.CharField(max_length=32, unique=True, description='用户名')
    password = fields.CharField(max_length=128, null=False, description='密码')
    phone = fields.CharField(max_length=11, null=True, description='手机号')
    intro = fields.CharField(max_length=128, null=True, description='简介')

    # 权限相关字段
    role = fields.CharEnumField(Role, max_length=16, default=Role.STAFF, null=False, description='角色')
    hid = fields.IntField(null=True, description='关联门店ID（店长和员工必填）')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    exhihall: ExhiHall | None = None

    class Meta:  # type: ignore
        table = 'admin_user'
        ordering: ClassVar[list[str]] = ['id']

    @property
    def is_authenticated(self) -> bool:
        return True

    @property
    def display_name(self) -> str:
        return self.username

    def verify_password(self, plain_password):
        """验证密码"""
        return verify_signature(plain_password, self.password, secret_key=SECRET_KEY)

    @staticmethod
    def hash_password(plain_password):
        return make_signature(plain_password, secret_key=SECRET_KEY)

    @relate_cache
    async def load_exhihall(self):
        """加载关联的门店信息"""
        if self.hid:
            return await ExhiHall.get(id=self.hid)
        return None

    def can_manage_store(self, hid: int) -> bool:
        """检查是否可以管理指定门店"""
        if self.role in [Role.SUPER, Role.ADMIN]:
            return True
        else:
            return self.hid == hid

    @cached_property
    def can_create_roles(self) -> list[Role]:
        """列举当前可以创建的角色"""
        return {
            Role.SUPER: [Role.SUPER, Role.ADMIN, Role.MANAGER, Role.STAFF, Role.OPERATOR],
            Role.ADMIN: [Role.MANAGER, Role.STAFF, Role.OPERATOR],
            Role.MANAGER: [Role.STAFF],
            Role.STAFF: [],
            Role.OPERATOR: [],
        }[self.role]

    @cached_property
    def can_view_users(self) -> bool:
        """是否可以查看用户"""
        return self.role in [
            Role.SUPER,
            Role.ADMIN,
            Role.MANAGER,
            Role.STAFF,
            Role.OPERATOR,
        ]

    @cached_property
    def can_edit_users(self) -> bool:
        """是否可以编辑用户"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER]

    @cached_property
    def can_delete_users(self) -> bool:
        """是否可以删除用户"""
        return self.role == Role.SUPER

    @cached_property
    def can_view_orders(self) -> bool:
        """是否可以查看订单"""
        return self.role in [
            Role.SUPER,
            Role.ADMIN,
            Role.MANAGER,
            Role.STAFF,
            Role.OPERATOR,
        ]

    @cached_property
    def can_create_orders(self) -> bool:
        """是否可以创建订单"""
        return self.role == Role.SUPER

    @cached_property
    def can_edit_orders(self) -> bool:
        """是否可以编辑订单"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER]

    @cached_property
    def can_delete_orders(self) -> bool:
        """是否可以删除订单"""
        return self.role == Role.SUPER

    @cached_property
    def can_checkin_tickets(self) -> bool:
        """是否可以验票"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER, Role.STAFF]

    @cached_property
    def can_manage_exhibitions(self) -> bool:
        """是否可以管理展览"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_manage_halls(self) -> bool:
        """是否可以管理展馆"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_manage_tickets(self) -> bool:
        """是否可以管理门票"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_manage_admins(self) -> bool:
        """是否可以管理管理员"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER]

    @cached_property
    def can_manage_settings(self) -> bool:
        """是否可以管理系统设置"""
        return self.role in [Role.SUPER, Role.ADMIN]


class Setting(Model):
    """系统设置表"""

    class Type(StrEnum):
        INT = 'int'
        FLOAT = 'float'
        BOOL = 'bool'
        STR = 'str'
        TEXT = 'text'
        JSON = 'json'

    name = fields.CharField(max_length=32, unique=True, description='配置项名称')
    vtype = fields.CharEnumField(Type, max_length=8, null=False, description='配置项类型')
    vint = fields.IntField(null=True, description='整型值')
    vfloat = fields.FloatField(null=True, description='浮点型值')
    vstr = fields.CharField(max_length=256, null=True, description='字符串值')
    vtext = fields.TextField(null=True, description='文本值')
    vjson: Any = fields.JSONField(null=True, description='JSON值')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    @property
    def value(self):
        match self.vtype:
            case self.Type.INT:
                return self.vint
            case self.Type.FLOAT:
                return self.vfloat
            case self.Type.BOOL:
                return bool(self.vint)
            case self.Type.STR:
                return self.vstr
            case self.Type.TEXT:
                return self.vtext
            case self.Type.JSON:
                return self.vjson

        raise TypeError(f'Invalid setting type: {self.vtype}')

    @classmethod
    async def value_of(cls, name):
        if obj := await cls.get_or_none(name=name):
            return obj.value
        return None

    @classmethod
    async def values(cls, *keys) -> AttrDict:
        if keys:
            return AttrDict({o.name: o.value for o in await cls.filter(name__in=keys)})
        else:
            return AttrDict({o.name: o.value for o in await cls.all()})
