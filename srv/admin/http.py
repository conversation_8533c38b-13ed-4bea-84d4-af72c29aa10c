from fastapi import APIRouter, Request, Response
from fastapi.templating import <PERSON><PERSON>2Templates

import config as cfg
from libs.http import abort
from libs.http import render as render_json
from libs.state import State

router = APIRouter()
templates = Jinja2Templates(directory='admin/templates')

__all__ = ['abort', 'render_html', 'render_json', 'router']

BREADCRUMBS = {
    # 一级
    'adm': '首页',
    # 二级
    'order': '订单',
    'user': '用户',
    'settings': '设置',
    'exhibition': '展览',
    'exhihall': '展馆',
    'ticket': '门票',
    'joint': '联票',
    'manager': '人员',
    # 三级
    'form': '创建/修改',
    'detail': '详情',
    'save': '保存',
    'delete': '删除',
}


def breadcrumbs(request: Request, context: dict) -> list[dict[str, str]]:
    """生成面包屑导航数据"""
    path = request.url.path
    breadcrumbs = []
    if path.startswith('/adm'):
        parts = filter(lambda p: p, path.strip('/').split('/'))
        parent_path = '/'
        for part in parts:
            if part not in BREADCRUMBS:
                break
            parent_path = f'{parent_path}{part}/'
            if part == 'form':
                name = '修改' if context.get('is_edit') else '创建'
            else:
                name = BREADCRUMBS[part]
            breadcrumbs.append({'name': name, 'url': parent_path})
    return breadcrumbs


def render_html(template_name: str, context: dict | None = None) -> Response:
    """渲染模板的辅助函数"""
    if context is None:
        context = {}
    request: Request = State.get('request')
    context['request'] = request
    context['adm'] = State.get('user')
    context['breadcrumbs'] = breadcrumbs(request, context)
    context['cfg'] = cfg
    return templates.TemplateResponse(template_name, context)
