from collections.abc import Callable
from functools import wraps

from fastapi import HTTPException
from fastapi.responses import RedirectResponse

from admin.models import Admin, Role
from libs.state import State


def require_role(*allowed_roles: Role):
    """
    要求特定角色的装饰器

    Args:
        allowed_roles: 允许的角色列表
    """

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            user = State.get('user')
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)

            # 检查角色
            if user.role not in allowed_roles:
                raise HTTPException(status_code=403, detail='角色权限不足')

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def require_permission(permission_method: str):
    """
    要求特定权限的装饰器

    Args:
        permission_method: Admin模型中的权限检查方法名，如 'can_view_users'
    """

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            user = State.get('user')
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)

            # 检查权限
            if not getattr(user, permission_method, None):
                raise HTTPException(status_code=403, detail='权限不足')

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def check_store_permission(user: Admin, hid: int) -> bool:
    """
    检查用户是否可以访问指定门店的数据

    Args:
        user: 管理员用户
        hid: 门店ID

    Returns:
        bool: 是否有权限访问
    """
    return user.can_manage_store(hid)


async def filter_stores_by_permission(user: Admin, hids: list[int] | None = None) -> list[int]:
    """
    根据用户权限过滤门店ID列表

    Args:
        user: 管理员用户
        hids: 门店ID列表，如果为None则返回用户有权限的所有门店

    Returns:
        list[int]: 用户有权限访问的门店ID列表
    """
    if user.role in [Role.SUPER, Role.ADMIN, Role.OPERATOR]:
        # 超级管理员、普通管理员和运营账号可以访问所有门店
        if hids is None:
            from apps.exhi.models import ExhiHall

            halls = await ExhiHall.all()
            return [hall.id for hall in halls]
        return hids
    elif user.role in [Role.MANAGER, Role.STAFF]:
        # 店长和员工只能访问自己的门店
        if user.hid:
            if hids is None:
                return [user.hid]
            return [hid for hid in hids if hid == user.hid]

    return []


# 常用的权限装饰器组合
def require_user_view():
    """要求用户查看权限"""
    return require_permission('can_view_users')


def require_user_create():
    """要求用户创建权限"""
    return require_permission('can_create_users')


def require_user_edit():
    """要求用户编辑权限"""
    return require_permission('can_edit_users')


def require_user_delete():
    """要求用户删除权限"""
    return require_permission('can_delete_users')


def require_order_view():
    """要求订单查看权限"""
    return require_permission('can_view_orders')


def require_order_create():
    """要求订单创建权限"""
    return require_permission('can_create_orders')


def require_order_edit():
    """要求订单编辑权限"""
    return require_permission('can_edit_orders')


def require_order_delete():
    """要求订单删除权限"""
    return require_permission('can_delete_orders')


def require_ticket_checkin():
    """要求验票权限"""
    return require_permission('can_checkin_tickets')


def require_admin_manage():
    """要求管理员管理权限"""
    return require_permission('can_manage_admins')


def require_exhibition_manage():
    """要求展览管理权限"""
    return require_permission('can_manage_exhibitions')


def require_hall_manage():
    """要求展馆管理权限"""
    return require_permission('can_manage_halls')


def require_ticket_manage():
    """要求门票管理权限"""
    return require_permission('can_manage_tickets')


def require_settings_manage():
    """要求系统设置管理权限"""
    return require_permission('can_manage_settings')


# 角色装饰器快捷方式
def require_super_admin():
    """只允许超级管理员"""
    return require_role(Role.SUPER)


def require_admin_or_above():
    """允许管理员及以上角色"""
    return require_role(Role.SUPER, Role.ADMIN)


def require_manager_or_above():
    """允许店长及以上角色"""
    return require_role(Role.SUPER, Role.ADMIN, Role.MANAGER)


def require_staff_or_above():
    """允许员工及以上角色（除运营）"""
    return require_role(Role.SUPER, Role.ADMIN, Role.MANAGER, Role.STAFF)
